# AKShare 后端服务独立启动配置
# 用于单独启动和测试AKShare后端服务

version: '3.8'

services:
  # AKShare 后端数据服务
  akshare-backend:
    build:
      context: ../backend/akshare-service
      dockerfile: Dockerfile
    container_name: tradingagents-akshare-backend
    ports:
      - "5000:5000"
    environment:
      - APP_NAME=AKShare数据服务
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=5000
      - LOG_LEVEL=INFO
      - ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:80"]
    volumes:
      # 开发模式下挂载代码目录，支持热重载
      - ../backend/akshare-service/app:/app/app
    networks:
      - akshare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  akshare-network:
    driver: bridge
    name: akshare-network
