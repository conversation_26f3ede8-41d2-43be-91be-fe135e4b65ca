"""
AKShare 后端服务主文件
提供金融数据获取的RESTful API接口

这个服务将akshare的数据获取功能封装为API，
供前端通过HTTP请求调用，而不是在前端直接运行Python代码。
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import akshare as ak
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import logging
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title="AKShare 数据服务",
    description="提供中国金融市场数据的API服务",
    version="1.0.0",
    docs_url="/docs",  # Swagger UI 文档地址
    redoc_url="/redoc"  # ReDoc 文档地址
)

# 配置CORS中间件，允许前端跨域访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型定义
class StockHistoryRequest(BaseModel):
    """股票历史数据请求模型"""
    symbol: str  # 股票代码，如 "000001"
    period: str = "daily"  # 周期：daily, weekly, monthly
    start_date: Optional[str] = None  # 开始日期，格式：YYYYMMDD
    end_date: Optional[str] = None  # 结束日期，格式：YYYYMMDD
    adjust: str = ""  # 复权类型：qfq前复权, hfq后复权, 空字符串不复权

class StockNewsRequest(BaseModel):
    """股票新闻请求模型"""
    symbol: str  # 股票代码
    limit: int = 20  # 新闻数量限制

class TechnicalIndicatorsRequest(BaseModel):
    """技术指标请求模型"""
    symbol: str  # 股票代码
    indicator: str = "ma"  # 指标类型：ma, macd, rsi等
    period: int = 20  # 计算周期

# 工具函数
def safe_json_convert(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """
    安全地将DataFrame转换为JSON格式
    处理日期和NaN值等特殊情况
    """
    try:
        # 处理日期列
        for col in df.columns:
            if df[col].dtype == 'datetime64[ns]':
                df[col] = df[col].dt.strftime('%Y-%m-%d')
        
        # 处理索引如果是日期类型
        if isinstance(df.index, pd.DatetimeIndex):
            df.index = df.index.strftime('%Y-%m-%d')
        
        # 将DataFrame转换为字典列表
        result = df.fillna("").to_dict('records')
        return result
    except Exception as e:
        logger.error(f"JSON转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据转换失败: {str(e)}")

# API路由定义

@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {
        "service": "AKShare 数据服务",
        "version": "1.0.0",
        "status": "运行中",
        "docs": "/docs",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 简单测试akshare是否可用
        ak.stock_zh_a_spot_em()  # 获取A股实时行情，测试连接
        return {"status": "healthy", "akshare": "available"}
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {"status": "unhealthy", "error": str(e)}

@app.post("/api/stock/history")
async def get_stock_history(request: StockHistoryRequest):
    """
    获取股票历史数据
    
    参数:
    - symbol: 股票代码（如：000001）
    - period: 周期（daily/weekly/monthly）
    - start_date: 开始日期（YYYYMMDD格式）
    - end_date: 结束日期（YYYYMMDD格式）
    - adjust: 复权类型（qfq/hfq/空字符串）
    """
    try:
        logger.info(f"获取股票历史数据: {request.symbol}")
        
        # 设置默认日期范围（最近一年）
        if not request.start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")
        else:
            start_date = request.start_date
            
        if not request.end_date:
            end_date = datetime.now().strftime("%Y%m%d")
        else:
            end_date = request.end_date
        
        # 调用akshare获取数据
        df = ak.stock_zh_a_hist(
            symbol=request.symbol,
            period=request.period,
            start_date=start_date,
            end_date=end_date,
            adjust=request.adjust
        )
        
        if df.empty:
            raise HTTPException(status_code=404, detail="未找到股票数据")
        
        # 转换为JSON格式
        data = safe_json_convert(df)
        
        return {
            "success": True,
            "data": data,
            "count": len(data),
            "symbol": request.symbol,
            "period": request.period
        }
        
    except Exception as e:
        logger.error(f"获取股票历史数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@app.post("/api/stock/news")
async def get_stock_news(request: StockNewsRequest):
    """
    获取股票相关新闻
    
    参数:
    - symbol: 股票代码
    - limit: 新闻数量限制
    """
    try:
        logger.info(f"获取股票新闻: {request.symbol}")
        
        # 获取股票新闻（这里使用通用新闻接口，实际可能需要根据具体需求调整）
        df = ak.stock_news_em()  # 东方财富股票新闻
        
        if df.empty:
            return {
                "success": True,
                "data": [],
                "count": 0,
                "symbol": request.symbol
            }
        
        # 限制新闻数量
        df = df.head(request.limit)
        
        # 转换为JSON格式
        data = safe_json_convert(df)
        
        return {
            "success": True,
            "data": data,
            "count": len(data),
            "symbol": request.symbol
        }
        
    except Exception as e:
        logger.error(f"获取股票新闻失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取新闻失败: {str(e)}")

@app.post("/api/stock/realtime")
async def get_stock_realtime(symbol: str):
    """
    获取股票实时数据
    
    参数:
    - symbol: 股票代码
    """
    try:
        logger.info(f"获取股票实时数据: {symbol}")
        
        # 获取A股实时行情
        df = ak.stock_zh_a_spot_em()
        
        # 筛选指定股票
        stock_data = df[df['代码'] == symbol]
        
        if stock_data.empty:
            raise HTTPException(status_code=404, detail="未找到股票实时数据")
        
        # 转换为JSON格式
        data = safe_json_convert(stock_data)
        
        return {
            "success": True,
            "data": data[0] if data else {},
            "symbol": symbol
        }
        
    except Exception as e:
        logger.error(f"获取股票实时数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取实时数据失败: {str(e)}")

@app.get("/api/market/overview")
async def get_market_overview():
    """
    获取市场概览数据
    包括主要指数、涨跌统计等
    """
    try:
        logger.info("获取市场概览数据")
        
        # 获取A股主要指数
        indices = ak.stock_zh_index_spot()
        
        # 获取涨跌统计
        up_down = ak.stock_zh_a_spot_em()
        
        # 计算涨跌统计
        total_stocks = len(up_down)
        up_stocks = len(up_down[up_down['涨跌幅'] > 0])
        down_stocks = len(up_down[up_down['涨跌幅'] < 0])
        flat_stocks = total_stocks - up_stocks - down_stocks
        
        return {
            "success": True,
            "data": {
                "indices": safe_json_convert(indices.head(10)),  # 主要指数
                "statistics": {
                    "total": total_stocks,
                    "up": up_stocks,
                    "down": down_stocks,
                    "flat": flat_stocks,
                    "up_ratio": round(up_stocks / total_stocks * 100, 2)
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取市场概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取市场概览失败: {str(e)}")

# 异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "内部服务器错误",
            "timestamp": datetime.now().isoformat()
        }
    )

if __name__ == "__main__":
    # 启动服务器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=5000,
        reload=True,  # 开发模式下启用热重载
        log_level="info"
    )
