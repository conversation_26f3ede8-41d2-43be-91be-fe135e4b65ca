/**
 * 认证Axios测试脚本
 * 用于测试使用axios进行认证请求是否正确携带cookie
 * 
 * 这个脚本会：
 * 1. 测试用户登录并检查cookie设置
 * 2. 测试使用axios访问受保护的API
 * 3. 测试登出并检查cookie清除
 */

const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  username: '测试Axios用户',
  password: 'Test@123456'
};

// 创建axios实例，模拟前端配置
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // 重要：允许发送和接收 cookies
});

/**
 * 测试用户注册
 */
async function testRegister() {
  console.log('\n🧪 测试用户注册...');
  
  try {
    const response = await api.post('/api/auth/register', TEST_USER);
    
    console.log('✅ 注册成功:', response.data.message);
    console.log('📊 用户信息:', response.data.user);
    console.log('🍪 响应头中的Set-Cookie:', response.headers['set-cookie']);
    
    return true;
  } catch (error) {
    if (error.response) {
      console.log('❌ 注册失败:', error.response.data.error);
    } else {
      console.error('❌ 注册请求失败:', error.message);
    }
    return false;
  }
}

/**
 * 测试用户登录
 */
async function testLogin() {
  console.log('\n🧪 测试用户登录...');
  
  try {
    const response = await api.post('/api/auth/login', {
      email: TEST_USER.email,
      password: TEST_USER.password,
    });
    
    console.log('✅ 登录成功:', response.data.message);
    console.log('📊 用户信息:', response.data.user);
    console.log('🍪 响应头中的Set-Cookie:', response.headers['set-cookie']);
    
    return true;
  } catch (error) {
    if (error.response) {
      console.log('❌ 登录失败:', error.response.data.error);
    } else {
      console.error('❌ 登录请求失败:', error.message);
    }
    return false;
  }
}

/**
 * 测试访问受保护的API
 */
async function testProtectedAPI() {
  console.log('\n🧪 测试访问受保护的API...');
  
  try {
    const response = await api.get('/api/auth/me');
    
    console.log('✅ 成功访问受保护的API');
    console.log('📊 当前用户信息:', response.data.user);
    
    return true;
  } catch (error) {
    if (error.response) {
      console.log('❌ 访问受保护的API失败:', error.response.data.error);
    } else {
      console.error('❌ 访问受保护的API请求失败:', error.message);
    }
    return false;
  }
}

/**
 * 测试用户登出
 */
async function testLogout() {
  console.log('\n🧪 测试用户登出...');
  
  try {
    const response = await api.post('/api/auth/logout');
    
    console.log('✅ 登出成功:', response.data.message);
    console.log('🍪 响应头中的Set-Cookie:', response.headers['set-cookie']);
    
    return true;
  } catch (error) {
    if (error.response) {
      console.log('❌ 登出失败:', error.response.data.error);
    } else {
      console.error('❌ 登出请求失败:', error.message);
    }
    return false;
  }
}

/**
 * 验证登出后无法访问受保护的API
 */
async function testProtectedAPIAfterLogout() {
  console.log('\n🧪 测试登出后访问受保护的API...');
  
  try {
    const response = await api.get('/api/auth/me');
    
    console.log('❌ 登出后仍能访问受保护的API，这是一个安全问题');
    return false;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ 登出后正确拒绝访问受保护的API');
      return true;
    } else {
      console.error('❌ 测试请求失败:', error.message);
      return false;
    }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行认证Axios测试...');
  console.log('📍 测试服务器:', BASE_URL);
  console.log('👤 测试用户:', TEST_USER.email);
  console.log('🔧 使用axios配置: withCredentials=true');
  
  const results = [];
  
  // 运行测试序列
  results.push(await testRegister());
  results.push(await testProtectedAPI());
  results.push(await testLogin());
  results.push(await testProtectedAPI());
  results.push(await testLogout());
  results.push(await testProtectedAPIAfterLogout());
  
  // 统计结果
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log('\n📊 测试结果统计:');
  console.log(`✅ 通过: ${passed}/${total}`);
  console.log(`❌ 失败: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 所有测试通过！Axios认证系统工作正常。');
    console.log('💡 这意味着：');
    console.log('   - Cookie在登录/注册时正确设置');
    console.log('   - Axios自动携带cookie到后续请求');
    console.log('   - 受保护的API正确验证cookie');
    console.log('   - 登出时正确清除cookie');
  } else {
    console.log('\n⚠️  部分测试失败，请检查：');
    console.log('   - 服务器是否正在运行 (npm run dev)');
    console.log('   - JWT密钥是否正确配置');
    console.log('   - Cookie设置和CORS配置是否正确');
  }
  
  return passed === total;
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

// 导出函数供其他模块使用
module.exports = {
  runAllTests,
  testRegister,
  testLogin,
  testProtectedAPI,
  testLogout,
  testProtectedAPIAfterLogout,
};
