import axios from 'axios';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

// 创建 axios 实例
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // 重要：允许发送和接收 cookies
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从 sessionManager 获取 sessionId 并添加到请求头
    const sessionId = typeof window !== 'undefined' ? localStorage.getItem('sessionId') : null;
    if (sessionId) {
      config.headers['X-Session-ID'] = sessionId;
    }
    config.withCredentials = true;

    // 也可以从 cookie 获取 token（如果需要）
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 分析配置接口
export interface AnalysisConfig {
  ticker: string;
  analysisDate: string;
  selectedAnalysts: string[];
  llmProvider: string;
  deepThinkLlm: string;
  quickThinkLlm: string;
  maxDebateRounds: number;
  maxRiskDiscussRounds: number;
  onlineTools: boolean;
  researchDepth: string;
}

// 分析状态接口
export interface AnalysisState {
  currentStage: string;
  progress: number;
  isComplete: boolean;
  error?: string;
}

// 代理状态接口
export interface AgentStatus {
  id: string;
  name: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number;
  lastUpdate: string;
  message?: string;
}

// 分析报告接口
export interface AnalysisReport {
  type: string;
  title: string;
  content: string;
  timestamp: string;
  agent: string;
}

// 交易决策接口
export interface TradingDecision {
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  reasoning: string;
  riskLevel: 'low' | 'medium' | 'high';
  targetPrice?: number;
  stopLoss?: number;
  positionSize?: number;
  timeHorizon?: string;
  timestamp: string;
}

// 认证相关接口
export interface User {
  id: number;
  email: string;
  username: string;
  email_verified: boolean;
  avatar_url?: string;
  role: string;
  created_at: string;
  last_login_at?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user: User;
  sessionId: string;
}

// 认证API方法
export const authApi = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await api.post('/api/auth/login', credentials);
    return response.data;
  },

  // 用户注册
  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    const response = await api.post('/api/auth/register', userData);
    return response.data;
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<{ success: boolean; user: User }> => {
    const response = await api.get('/api/auth/me');
    return response.data;
  },

  // 用户登出
  logout: async (): Promise<{ success: boolean; message: string }> => {
    const response = await api.post('/api/auth/logout');
    return response.data;
  },
};

// 任务相关API方法
export const taskApi = {
  // 获取任务列表
  getTasks: async (): Promise<any[]> => {
    const response = await api.get('/api/database/tasks');
    return response.data;
  },

  // 更新任务状态
  updateTaskStatus: async (taskId: string, status: string): Promise<void> => {
    await api.patch(`/api/database/tasks/${taskId}/status`, { status });
  },

  // 启动任务分析
  startAnalysis: async (taskData: any): Promise<any> => {
    const response = await api.post('/api/langgraph/analyze', taskData);
    return response.data;
  },
};

// API 方法
export const tradingApi = {
  // 开始分析
  startAnalysis: async (config: AnalysisConfig): Promise<{ analysisId: string }> => {
    const response = await api.post('/api/langgraph/analysis/start', config);
    return response.data;
  },

  // 获取分析状态
  getAnalysisStatus: async (analysisId: string): Promise<AnalysisState> => {
    const response = await api.get(`/api/langgraph/analysis/status?analysisId=${analysisId}`);
    return response.data;
  },

  // 获取代理状态
  getAgentStatuses: async (analysisId: string): Promise<AgentStatus[]> => {
    const response = await api.get(`/api/langgraph/analysis/agents?analysisId=${analysisId}`);
    return response.data.agents;
  },

  // 获取分析报告
  getReports: async (analysisId: string): Promise<AnalysisReport[]> => {
    const response = await api.get(`/api/langgraph/analysis/reports?analysisId=${analysisId}`);
    return response.data.reports;
  },

  // 获取交易决策
  getTradingDecision: async (analysisId: string): Promise<TradingDecision | null> => {
    try {
      const response = await api.get(`/api/langgraph/analysis/decision?analysisId=${analysisId}`);
      return response.data.tradingDecision;
    } catch (error) {
      // 如果还没有决策，返回 null
      return null;
    }
  },

  // 停止分析
  stopAnalysis: async (analysisId: string): Promise<void> => {
    await api.delete(`/api/langgraph/analysis/status?analysisId=${analysisId}`);
  },

  // 获取股票实时数据
  getStockData: async (ticker: string, date: string): Promise<any> => {
    const response = await api.get(`/api/data/stock/${ticker}`, {
      params: { date },
    });
    return response.data;
  },

  // 获取新闻数据
  getNewsData: async (ticker: string, date: string): Promise<any> => {
    const response = await api.get(`/api/data/news/${ticker}`, {
      params: { date },
    });
    return response.data;
  },

  // 获取技术指标数据
  getTechnicalIndicators: async (ticker: string, date: string): Promise<any> => {
    const response = await api.get(`/api/data/technical/${ticker}`, {
      params: { date },
    });
    return response.data;
  },

  // 获取基本面数据
  getFundamentalsData: async (ticker: string, date: string): Promise<any> => {
    const response = await api.get(`/api/data/fundamentals/${ticker}`, {
      params: { date },
    });
    return response.data;
  },

  // 健康检查
  healthCheck: async (): Promise<{ status: string; version: string }> => {
    const response = await api.get('/api/health');
    return response.data;
  },
};

// WebSocket 连接管理
export class TradingWebSocket {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(analysisId: string) {
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
    this.url = `${wsUrl}/ws/analysis/${analysisId}`;
  }

  connect(onMessage: (data: any) => void, onError?: (error: Event) => void): void {
    try {
      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.attemptReconnect(onMessage, onError);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        if (onError) {
          onError(error);
        }
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      if (onError) {
        onError(error as Event);
      }
    }
  }

  private attemptReconnect(onMessage: (data: any) => void, onError?: (error: Event) => void): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
      );

      setTimeout(() => {
        this.connect(onMessage, onError);
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  send(data: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket is not connected');
    }
  }
}
