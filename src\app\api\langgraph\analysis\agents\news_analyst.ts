import { akshareAdapter } from '@/lib/akshare/adapter';
import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AKShareNewsItem } from '@/types';
import { AIMessage } from '@langchain/core/messages';

export async function newsAnalystNode(
  state: typeof TradingAgentAnnotation.State
): Promise<Partial<typeof TradingAgentAnnotation.State>> {
  const ticker = state.ticker;
  if (!ticker) {
    throw new Error('Ticker not found in state');
  }

  console.log(`[News Analyst] Analyzing ticker: ${ticker}`);

  try {
    const newsData = await akshareAdapter.invoke<AKShareNewsItem[]>('get_stock_news', {
      symbol: ticker,
    });

    if (!Array.isArray(newsData) || newsData.length === 0) {
      throw new Error('Failed to retrieve valid news data.');
    }

    // 提取最新的3条新闻标题作为摘要
    const summary = newsData
      .slice(0, 3)
      .map((item) => `- ${item.标题}`)
      .join('\n');

    const analysis = `对 ${ticker} 的最新新闻摘要：\n${summary}`;

    const newMessages = [...state.messages, new AIMessage(analysis)];
    const analysisResults = {
      ...state.analysis,
      news: {
        summary: analysis,
        data: newsData,
      },
    };

    return { messages: newMessages, analysis: analysisResults };
  } catch (error) {
    console.error('[News Analyst] Error:', error);
    const errorMessage = `新闻分析失败: ${error instanceof Error ? error.message : String(error)}`;
    const newMessages = [...state.messages, new AIMessage(errorMessage)];
    return { messages: newMessages };
  }
}
