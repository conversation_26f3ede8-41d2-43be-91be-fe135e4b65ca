#!/bin/bash
# TradingAgents Frontend Docker 启动脚本

echo "🚀 启动 TradingAgents Frontend Docker 容器..."

# 检查是否存在 .env 文件
if [ ! -f ".env" ]; then
    echo "⚠️  未找到 .env 文件，正在复制 .env.example..."
    cp .env.example .env
    echo "📝 请编辑 .env 文件并填入正确的 API 密钥"
    echo "   - NEXT_PUBLIC_OPENAI_API_KEY"
    echo "   - NEXT_PUBLIC_FINNHUB_API_KEY"
    
    read -p "是否继续启动? (y/N): " continue
    if [[ $continue != "y" && $continue != "Y" ]]; then
        echo "❌ 启动已取消"
        exit 1
    fi
fi

# 选择启动模式
echo ""
echo "请选择启动模式:"
echo "1. 仅前端 (推荐用于开发)"
echo "2. 完整服务 (前端 + AKShare后端 + MySQL + Nginx)"
echo "3. 前端 + MySQL"
echo "4. 仅AKShare后端服务 (用于测试后端API)"
echo "5. 前端 + AKShare后端 (推荐用于完整开发)"

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "🔧 启动仅前端服务..."
        docker-compose -f docker-compose-simple.yml up --build -d
        ;;
    2)
        echo "🔧 启动完整服务..."
        docker-compose up --build -d
        ;;
    3)
        echo "🔧 启动前端和数据库..."
        docker-compose up frontend mysql --build -d
        ;;
    4)
        echo "🔧 启动仅AKShare后端服务..."
        docker-compose -f docker-compose.akshare.yml up --build -d
        ;;
    5)
        echo "🔧 启动前端和AKShare后端..."
        docker-compose up frontend akshare-backend --build -d
        ;;
    *)
        echo "❌ 无效选择，默认启动仅前端服务"
        docker-compose -f docker-compose-simple.yml up --build -d
        ;;
esac

echo ""
echo "✅ 启动完成!"
echo "🌐 前端地址: http://localhost:3000"
echo "🔗 AKShare后端API: http://localhost:5000"
echo "📖 API文档: http://localhost:5000/docs"
echo "📊 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"
echo ""
echo "💡 提示："
echo "   - 如果选择了AKShare后端，请等待后端服务完全启动后再使用前端"
echo "   - 可以通过 http://localhost:5000/health 检查后端服务状态"
