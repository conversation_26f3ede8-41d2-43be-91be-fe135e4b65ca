# AKShare 后端迁移指南

本文档说明了如何将 AKShare 相关代码从前端迁移到 Docker 容器后端的完整过程。

## 🎯 迁移目标

将原本在前端运行的 AKShare Python 代码迁移到独立的后端服务中，实现：

1. **更好的架构分离**: 前端专注于 UI，后端处理数据
2. **更高的性能**: 后端服务可以复用连接和缓存数据
3. **更好的安全性**: Python 代码在隔离的容器中运行
4. **更好的可维护性**: 前后端职责明确，便于维护和扩展

## 📋 迁移内容

### 🔄 架构变更

#### 迁移前（旧架构）
```
前端 Next.js
├── akshare 适配器 (TypeScript)
├── Python 子进程
├── akshare_worker.py
└── 直接调用 akshare 库
```

#### 迁移后（新架构）
```
前端 Next.js                    后端 Python 服务
├── HTTP API 客户端      ←→     ├── FastAPI 应用
├── akshare 适配器 (重构)        ├── akshare 库集成
└── 标准 HTTP 请求              └── RESTful API 接口
```

### 📁 新增文件结构

```
backend/akshare-service/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI 主应用
│   └── config.py            # 配置管理
├── Dockerfile               # Docker 镜像构建
├── requirements.txt         # Python 依赖
├── .env.example            # 环境变量模板
├── README.md               # 服务文档
└── test_api.py             # API 测试脚本

docker/
├── docker-compose.akshare.yml  # AKShare 服务独立配置
└── docker-compose.yml          # 更新的完整配置
```

### 🔧 修改的文件

1. **src/lib/akshare/adapter.ts** - 完全重构为 HTTP API 客户端
2. **docker/docker-compose.yml** - 添加 AKShare 后端服务
3. **docker/start.sh** - 添加新的启动选项
4. **docker/start.ps1** - 添加新的启动选项

## 🚀 使用指南

### 1. 启动服务

#### 方式一：使用启动脚本（推荐）

```bash
# Linux/Mac
cd docker
./start.sh

# Windows
cd docker
./start.ps1

# 选择选项 5: 前端 + AKShare后端 (推荐用于完整开发)
```

#### 方式二：手动启动

```bash
# 启动 AKShare 后端和前端
docker-compose up frontend akshare-backend --build -d

# 或启动完整服务
docker-compose up --build -d
```

#### 方式三：仅启动后端（用于测试）

```bash
# 仅启动 AKShare 后端服务
docker-compose -f docker-compose.akshare.yml up --build -d
```

### 2. 验证服务

#### 检查服务状态

```bash
# 检查容器状态
docker-compose ps

# 查看日志
docker-compose logs -f akshare-backend
```

#### 测试 API 接口

```bash
# 健康检查
curl http://localhost:5000/health

# 查看 API 文档
# 浏览器访问: http://localhost:5000/docs
```

#### 运行自动化测试

```bash
# 进入后端服务目录
cd backend/akshare-service

# 运行测试脚本
python test_api.py
```

### 3. 前端集成

前端的 akshare 适配器已经自动更新，无需修改现有的调用代码：

```typescript
// 原有代码保持不变
const { analyzeStock } = useLangGraphAgent();
await analyzeStock(ticker, config);

// 或直接使用适配器
import { akshareAdapter } from '@/lib/akshare/adapter';
const data = await akshareAdapter.invoke('get_stock_history', {
  symbol: '000001',
  period: 'daily'
});
```

## 🔗 API 接口说明

### 基础接口

- `GET /` - 服务信息
- `GET /health` - 健康检查
- `GET /docs` - Swagger UI 文档

### 数据接口

- `POST /api/stock/history` - 股票历史数据
- `POST /api/stock/news` - 股票新闻
- `POST /api/stock/realtime` - 股票实时数据
- `GET /api/market/overview` - 市场概览

### 请求示例

```bash
# 获取股票历史数据
curl -X POST "http://localhost:5000/api/stock/history" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "000001",
    "period": "daily",
    "start_date": "20240101",
    "end_date": "20241231"
  }'
```

## 🛠️ 开发指南

### 本地开发

如果需要修改后端代码：

```bash
# 进入后端目录
cd backend/akshare-service

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn app.main:app --host 0.0.0.0 --port 5000 --reload
```

### 添加新的 API 接口

1. 在 `backend/akshare-service/app/main.py` 中添加新的路由
2. 在前端 `src/lib/akshare/adapter.ts` 中添加对应的方法
3. 更新测试脚本 `backend/akshare-service/test_api.py`

## 🔧 配置说明

### 环境变量

主要的环境变量配置：

```env
# 前端环境变量
AKSHARE_API_URL=http://localhost:5000          # 本地开发
AKSHARE_API_URL=http://akshare-backend:5000     # Docker 环境

# 后端环境变量
HOST=0.0.0.0
PORT=5000
DEBUG=false
LOG_LEVEL=INFO
```

### Docker 网络

服务间通过 Docker 网络通信：

- 网络名称: `tradingagents-network`
- 前端容器: `tradingagents-frontend`
- 后端容器: `tradingagents-akshare-backend`
- 内部通信地址: `http://akshare-backend:5000`

## 🚨 故障排除

### 常见问题

1. **后端服务启动失败**
   ```bash
   # 检查日志
   docker-compose logs akshare-backend
   
   # 检查端口占用
   netstat -an | grep 5000
   ```

2. **前端无法连接后端**
   ```bash
   # 检查网络连接
   docker network ls
   docker network inspect tradingagents-network
   
   # 测试连接
   curl http://localhost:5000/health
   ```

3. **API 调用失败**
   ```bash
   # 运行测试脚本
   cd backend/akshare-service
   python test_api.py
   ```

### 调试模式

启用详细日志：

```bash
# 设置环境变量
DEBUG=true docker-compose up akshare-backend
```

## 📈 性能优化建议

1. **缓存**: 考虑添加 Redis 缓存频繁请求的数据
2. **连接池**: 优化数据库和外部 API 连接
3. **限流**: 实施 API 请求限流
4. **监控**: 添加性能监控和告警

## 🔒 安全考虑

1. **CORS**: 生产环境中限制允许的域名
2. **认证**: 添加 API 认证和授权
3. **HTTPS**: 使用 HTTPS 加密传输
4. **更新**: 定期更新依赖包

## 📚 相关文档

- [AKShare 后端服务 README](backend/akshare-service/README.md)
- [Docker 部署指南](docker/README.md)
- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [AKShare 官方文档](https://akshare.akfamily.xyz/)

## ✅ 迁移检查清单

- [ ] 后端服务成功启动
- [ ] 健康检查通过
- [ ] API 文档可访问
- [ ] 前端可以调用后端 API
- [ ] 股票数据获取正常
- [ ] 新闻数据获取正常
- [ ] 实时数据获取正常
- [ ] 市场概览数据正常
- [ ] 错误处理正常
- [ ] 日志记录正常

完成以上检查后，AKShare 后端迁移就完成了！🎉
