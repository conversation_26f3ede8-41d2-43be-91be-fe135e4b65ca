# AKShare 后端服务环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用配置
APP_NAME=AKShare数据服务
APP_VERSION=1.0.0
DEBUG=false

# 服务器配置
HOST=0.0.0.0
PORT=5000

# 日志配置
LOG_LEVEL=INFO

# CORS配置（生产环境中应该限制为特定域名）
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:80"]

# 缓存配置
CACHE_TTL=300

# API限制配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 数据库配置（可选）
# DATABASE_URL=mysql://user:password@localhost:3306/database

# Redis配置（可选，用于缓存）
# REDIS_URL=redis://localhost:6379/0
