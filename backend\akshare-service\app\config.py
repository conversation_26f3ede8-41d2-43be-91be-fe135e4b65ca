"""
AKShare 后端服务配置文件
管理服务的各种配置参数
"""

import os
from typing import Optional
from pydantic import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本配置
    app_name: str = "AKShare 数据服务"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 5000
    
    # CORS配置
    allowed_origins: list = ["*"]  # 生产环境中应该限制为特定域名
    
    # 日志配置
    log_level: str = "INFO"
    
    # 数据缓存配置
    cache_ttl: int = 300  # 缓存时间（秒）
    
    # API限制配置
    rate_limit_requests: int = 100  # 每分钟请求限制
    rate_limit_window: int = 60     # 限制窗口（秒）
    
    # 数据库配置（如果需要）
    database_url: Optional[str] = None
    
    # Redis配置（如果需要缓存）
    redis_url: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 创建全局配置实例
settings = Settings()

# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
        "detailed": {
            "formatter": "detailed",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
    },
    "root": {
        "level": settings.log_level,
        "handlers": ["default"],
    },
    "loggers": {
        "akshare": {
            "level": settings.log_level,
            "handlers": ["detailed"],
            "propagate": False,
        },
        "uvicorn": {
            "level": settings.log_level,
            "handlers": ["default"],
            "propagate": False,
        },
    },
}
